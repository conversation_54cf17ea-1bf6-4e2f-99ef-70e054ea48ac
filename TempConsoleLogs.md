uthorization header added to request for: /api/inbox/conversations/58422
ApiService.ts:259 🚀 API REQUEST: {method: 'GET', url: '/api/inbox/conversations/58422', baseURL: 'https://api.adtip.in', fullURL: 'https://api.adtip.in/api/inbox/conversations/58422', headers: {…}, params: {…}, data: undefined, timeout: 60000, timestamp: '2025-07-26T06:14:28.701Z'}
ApiService.ts:302 ❌ API ERROR RESPONSE: {method: 'GET', url: '/api/inbox/conversations/58422', baseURL: 'https://api.adtip.in', fullURL: 'https://api.adtip.in/api/inbox/conversations/58422', status: 404, statusText: undefined, headers: {…}, data: '<!DOCTYPE html>\n<html lang="en">\n<head>\n<meta charset="utf-8">\n<title>Error</title>\n</head>\n<body>\n<pre>Cannot GET /api/inbox/conversations/58422</pre>\n</body>\n</html>\n', timestamp: '2025-07-26T06:14:28.738Z'}baseURL: "https://api.adtip.in"data: "<!DOCTYPE html>\n<html lang=\"en\">\n<head>\n<meta charset=\"utf-8\">\n<title>Error</title>\n</head>\n<body>\n<pre>Cannot GET /api/inbox/conversations/58422</pre>\n</body>\n</html>\n"fullURL: "https://api.adtip.in/api/inbox/conversations/58422"headers: {access-control-allow-credentials: 'true', access-control-allow-headers: 'Origin, X-Requested-With, Content-Type, Accept', access-control-allow-methods: 'GET, POST, PUT, DELETE', access-control-allow-origin: '*', connection: 'keep-alive', content-length: '168', content-security-policy: "default-src 'none'", content-type: 'text/html; charset=utf-8', cross-origin-embedder-policy: 'require-corp', cross-origin-opener-policy: 'same-origin', …}method: "GET"status: 404statusText: undefinedtimestamp: "2025-07-26T06:14:28.738Z"url: "/api/inbox/conversations/58422"[[Prototype]]: Object
ApiService.ts:632 API Error Details (handleError): {isAxiosError: true, status: 404, statusText: undefined, data: '<!DOCTYPE html>\n<html lang="en">\n<head>\n<meta charset="utf-8">\n<title>Error</title>\n</head>\n<body>\n<pre>Cannot GET /api/inbox/conversations/58422</pre>\n</body>\n</html>\n', message: 'Request failed with status code 404', config: {…}}
LogUtils.ts:36 [InboxScreen] Error loading conversations: Error: Request failed with status code 404
    at handleError (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:143814:29)
    at ?anon_0_ (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:143651:35)
    at throw (native)
    at asyncGeneratorStep (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:1418:19)
    at _throw (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:1435:29)
    at tryCallOne (address at InternalBytecode.js:1:1180)
    at anonymous (address at InternalBytecode.js:1:1874)
anonymous @ console.js:654
overrideMethod @ backend.js:17042
reactConsoleErrorHandler @ ExceptionsManager.js:182
anonymous @ setUpDeveloperTools.js:40
registerError @ LogBox.js:231
anonymous @ LogBox.js:80
error @ LogUtils.ts:36
?anon_0_ @ InboxScreen.tsx:101
asyncGeneratorStep @ asyncToGenerator.js:3
_throw @ asyncToGenerator.js:20
Show 8 more frames
Show less
ApiService.ts:302 ❌ API ERROR RESPONSE: {method: 'GET', url: '/api/inbox/conversations/58422', baseURL: 'https://api.adtip.in', fullURL: 'https://api.adtip.in/api/inbox/conversations/58422', status: 404, statusText: undefined, headers: {…}, data: '<!DOCTYPE html>\n<html lang="en">\n<head>\n<meta charset="utf-8">\n<title>Error</title>\n</head>\n<body>\n<pre>Cannot GET /api/inbox/conversations/58422</pre>\n</body>\n</html>\n', timestamp: '2025-07-26T06:14:28.756Z'}
ApiService.ts:632 API Error Details (handleError): {isAxiosError: true, status: 404, statusText: undefined, data: '<!DOCTYPE html>\n<html lang="en">\n<head>\n<meta charset="utf-8">\n<title>Error</title>\n</head>\n<body>\n<pre>Cannot GET /api/inbox/conversations/58422</pre>\n</body>\n</html>\n', message: 'Request failed with status code 404', config: {…}}
LogUtils.ts:36 [InboxScreen] Error loading conversations: Error: Request failed with status code 404
    at handleError (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:143814:29)
    at ?anon_0_ (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:143651:35)
    at throw (native)
    at asyncGeneratorStep (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:1418:19)
    at _throw (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:1435:29)
    at tryCallOne (address at InternalBytecode.js:1:1180)
    at anonymous (address at InternalBytecode.js:1:1874)
anonymous @ console.js:654
overrideMethod @ backend.js:17042
reactConsoleErrorHandler @ ExceptionsManager.js:182
anonymous @ setUpDeveloperTools.js:40
registerError @ LogBox.js:231
anonymous @ LogBox.js:80
error @ LogUtils.ts:36
?anon_0_ @ InboxScreen.tsx:101
asyncGeneratorStep @ asyncToGenerator.js:3
_throw @ asyncToGenerator.js:20
Show 8 more frames
Show less
BannerAdComponent.tsx:61 🔄 [BannerAd] Auto-rotating to next ad network
AdRotationService.ts:71 🔄 [AdRotation] Switched to Business Collaboration (rotation #1)
AdRotationService.ts:93 📱 [AdRotation] Next Business Collaboration banner ad: /***********,***********/com.adtip.app.adtip_app.Banner0.1750928844